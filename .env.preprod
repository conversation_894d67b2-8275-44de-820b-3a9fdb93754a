APP_NAME="Mumzworld ratings-and-reviews"
APP_ENV=production
APP_KEY=base64:qaTsGB0As7B7Uwh/vR5qk6XS1YU1zarGkLnCKV3iVHw=
APP_DEBUG=true
APP_URL=http://ratings-and-reviews-preprod.mumzstage.com
APP_PORT=7000
NGINX_PORT=80

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file


PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack

LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=dynamo
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=database_name
DB_USERNAME=database_user
DB_PASSWORD=database_password

SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis

CACHE_STORE=redis
MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=cart-svc-staging.fgph2e.ng.0001.aps1.cache.amazonaws.com
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="mumzworld"

AWS_ACCESS_KEY_ID="********************"
AWS_SECRET_ACCESS_KEY="1NyxG30K5FUad6efQmWZ5isoF2GlcXytamQt4+Mq"
AWS_DEFAULT_REGION=ap-south-1

AWS_BUCKET= ratings-and-reviews-preprod
AWS_USE_PATH_STYLE_ENDPOINT=false
DYNAMODB_CONNECTION=https://dynamodb.ap-south-1.amazonaws.com
DOCKER_DYNAMODB_HOST_PORT=8000
DOCKER_DYNAMODB_ADMIN_HOST_PORT=8001

DYNAMODB_LOCAL_ENDPOINT=https://dynamodb.ap-south-1.amazonaws.com
DYNAMODB_DEBUG=true
VITE_APP_NAME=mumzworld


DOCKER_APP_DOCKERFILE=Dockerfile.prod # Options: Dockerfile.dev, Dockerfile.prod
DOCKER_APP_HTTP_PORT=7001
DOCKER_APP_HTTPS_PORT=443
DOCKER_PHPMYADMIN_HOST_PORT=8080
DOCKER_REDIS_COMMANDER_HOST_PORT=8081
DOCKER_HORIZON_DOCKERFILE=Dockerfile.prod # Options: Dockerfile.dev, Dockerfile.prod

GOOGLE_TRANSLATE_API_KEY=
GOOGLE_TRANSLATE_ENDPOINT=https://translation.googleapis.com/language/translate/v2

CLOUDFRONT_DISTRIBUTION_ID=E12YLKGAFPXAIF
CLOUDFRONT_KEY=********************
CLOUDFRONT_SECRET=1NyxG30K5FUad6efQmWZ5isoF2GlcXytamQt4+Mq
CLOUDFRONT_REGION=ap-south-1

DB_CONNECTION=dynamodb
DYNAMODB_TABLE=ratings-and-reviews-preprod
