# Mumzworld Ratings and Reviews Service

A comprehensive microservice for managing product ratings and reviews with support for media uploads, automatic translation, real-time statistics, and CloudFront cache invalidation.

## 🚀 Features

- **Review Management**: Create, retrieve, update, and delete product reviews
- **Advanced Filtering**: Filter reviews by product, user, country, language, and publication status
- **Multilingual Support**: Automatic translation between English and Arabic using Google Translate API
- **Media Upload**: Support for images and videos with configurable storage (local, public, S3)
- **Real-time Statistics**: Pre-calculated product rating statistics for fast API responses
- **Publication Workflow**: Configurable publication status management (pending, published, rejected)
- **CloudFront Integration**: Automatic cache invalidation for media files and API responses
- **Background Processing**: Queue-based statistics calculation and translation processing
- **Docker Support**: Complete containerized development environment

## 📋 Prerequisites

- **Docker** and **Docker Compose** (recommended)
- **PHP 8.2+** (if running without Docker)
- **Composer** (if running without Docker)
- **Git**

## 🛠️ Installation & Setup

### 1. Clone the Repository

```bash
git clone <repository-url>
cd mumzworld-ratings-and-reviews-service
```

### 2. Install Dependencies

```bash
composer install
```

### 3. Environment Configuration

Create your environment file from the example:

```bash
cp .env.example .env
```

**Required Environment Variables:**

```env
# Application
APP_NAME="Mumzworld Ratings and Reviews Service"
APP_ENV=local
APP_KEY=base64:your-app-key-here
APP_DEBUG=true
APP_URL=http://localhost

# Database (MySQL for sessions, cache, jobs)
DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=app_db
DB_USERNAME=app_user
DB_PASSWORD=userpassword

# DynamoDB Configuration
DYNAMODB_CONNECTION=local
DYNAMODB_LOCAL_ENDPOINT=http://dynamodb:8000
AWS_ACCESS_KEY_ID=dynamodblocal
AWS_SECRET_ACCESS_KEY=secret
AWS_DEFAULT_REGION=us-east-1

# Redis (for caching and queues)
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

# Queue Configuration
QUEUE_CONNECTION=redis
CACHE_DRIVER=redis
SESSION_DRIVER=redis

# Docker Configuration
DOCKER_APP_HTTP_PORT=7001
DOCKER_MYSQL_HOST_PORT=3306
DOCKER_REDIS_HOST_PORT=6379
DOCKER_DYNAMODB_HOST_PORT=8000
DOCKER_PHPMYADMIN_HOST_PORT=8080
DOCKER_REDIS_COMMANDER_HOST_PORT=8081
DOCKER_DYNAMODB_ADMIN_HOST_PORT=8001

# Optional: Google Translate API
GOOGLE_TRANSLATE_API_KEY=your-google-translate-api-key

# Optional: AWS S3 for media storage
AWS_BUCKET=your-s3-bucket
AWS_URL=https://your-cloudfront-domain.cloudfront.net

# Optional: CloudFront Cache Invalidation
CLOUDFRONT_DISTRIBUTION_ID=your-distribution-id
```

### 4. Generate Application Key

```bash
php artisan key:generate
```

### 5. Start Docker Services

```bash
docker-compose up -d
```

This will start the following services:
- **App**: Laravel application (port 7001)
- **MySQL**: Database server (port 3306)
- **Redis**: Cache and queue server (port 6379)
- **DynamoDB Local**: Local DynamoDB instance (port 8000)
- **Horizon**: Queue monitoring dashboard
- **phpMyAdmin**: MySQL administration (port 8080)
- **Redis Commander**: Redis administration (port 8081)
- **DynamoDB Admin**: DynamoDB administration (port 8001)

### 6. Run Database Migrations

**DynamoDB Migrations (for reviews and statistics):**
```bash
docker-compose exec app php artisan migrate:dynamodb
```

### 7. Seed the Database

**Seed with sample data:**
```bash
docker-compose exec app php artisan db:seed --class=RatingAndReviewSeeder
```

This will create:
- Reviews for 50,000 products (1-20 reviews each)
- 80% of reviews will have media attachments
- 10 published reviews with images for product ID: `12345678-1234-1234-1234-123456789012`
- 5 published reviews with videos for the same test product
- Reviews with different publication statuses (pending, published, rejected)

### 8. Start Queue Workers (Optional)

For background processing of statistics and translations:

```bash
docker-compose exec app php artisan horizon
```

Or start Horizon via the container:
```bash
docker-compose up horizon -d
```

### 9. Create Storage Link (if using public storage)

```bash
docker-compose exec app php artisan storage:link
```

## 🌐 Access Points

Once everything is running, you can access:

- **API Endpoints**: http://localhost:7001/api/
- **phpMyAdmin**: http://localhost:8080
- **Redis Commander**: http://localhost:8081
- **DynamoDB Admin**: http://localhost:8001
- **Horizon Dashboard**: http://localhost:7001/horizon

## 📚 API Documentation

### Core Endpoints

#### Customer-Facing Endpoints

```http
POST   /api/reviews                           # Create a review
GET    /api/reviews/{id}/translate            # Get translated review
GET    /api/products/{product_id}/reviews     # Get product reviews
GET    /api/products/{product_id}/rating      # Get product rating summary
POST   /api/products/ratings-summary          # Bulk rating summaries
```

#### Admin Endpoints

```http
GET    /api/reviews                           # Filter reviews by status
GET    /api/reviews/pending-check             # Check for pending reviews
GET    /api/reviews/counts-by-status-bk       # Get review counts by status
DELETE /api/reviews/{id}                      # Delete a review
PUT    /api/reviews/{id}/publication          # Update publication status
```

### Example API Calls

**Create a Review:**
```bash
curl -X POST http://localhost:7001/api/reviews \
  -H "Content-Type: application/json" \
  -d '{
    "product_id": "12345678-1234-1234-1234-123456789012",
    "user_id": "user123",
    "rating": 5,
    "title": "Great product!",
    "content": "I love this product, highly recommended.",
    "language": "en"
  }'
```

**Get Product Reviews:**
```bash
curl "http://localhost:7001/api/products/12345678-1234-1234-1234-123456789012/reviews?per_page=10&page=1"
```

**Get Product Rating Summary:**
```bash
curl "http://localhost:7001/api/products/12345678-1234-1234-1234-123456789012/rating"
```

For detailed API documentation, see [docs/API.md](docs/API.md) and [docs/openapi.yaml](docs/openapi.yaml).

## 🔧 Development Commands

### DynamoDB Commands

```bash
# Run DynamoDB migrations
docker-compose exec app php artisan migrate:dynamodb

# Run specific DynamoDB migration
docker-compose exec app php artisan migrate:dynamodb --file=2024_03_15_000001_create_ratings_and_reviews_table.php

# Fresh DynamoDB migrations (drops tables first)
docker-compose exec app php artisan migrate:dynamodb --fresh
```

### Translation Commands

```bash
# Translate published reviews (limit 100)
docker-compose exec app php artisan reviews:translate --limit=100 --status=published

# Translate all pending reviews
docker-compose exec app php artisan reviews:translate --status=pending
```

### Statistics Commands

```bash
# Backfill statistics for all products (use with caution on large datasets)
docker-compose exec app php artisan statistics:backfill

# Backfill statistics for specific products (recommended for targeted updates)
docker-compose exec app php artisan statistics:backfill --product_id=product1 --product_id=product2 --product_id=product3

# Process with custom chunk size to optimize memory usage and scan performance
docker-compose exec app php artisan statistics:backfill --chunk_size=50

# Combine options for targeted processing
docker-compose exec app php artisan statistics:backfill --product_id=high_traffic_product --chunk_size=25
```

**Performance Tips:**
- 🎯 **Use specific product IDs** when possible to avoid full table scans
- ⚡ **Adjust chunk size** based on your DynamoDB capacity and memory constraints
- 📊 **Monitor progress** - the command shows real-time progress for both ID fetching and processing
- 🕐 **Run during low traffic** periods for large datasets

### CloudFront Cache Commands

```bash
# Invalidate all cache
docker-compose exec app php artisan cloudfront:invalidate --all

# Invalidate specific review
docker-compose exec app php artisan cloudfront:invalidate --review=review_id

# Invalidate product API responses
docker-compose exec app php artisan cloudfront:invalidate --product=product_id
```

## 🛠️ Custom DynamoDB Artisan Commands

This project includes powerful custom artisan commands specifically designed to make working with DynamoDB easier and more intuitive. These commands bridge the gap between Laravel's traditional database tools and DynamoDB's NoSQL nature.

### DynamoDB Migration Commands

#### `migrate:dynamodb`
A comprehensive migration system for DynamoDB tables that mimics Laravel's traditional migration workflow.

```bash
# Run all DynamoDB migrations
docker-compose exec app php artisan migrate:dynamodb

# Run a specific migration file
docker-compose exec app php artisan migrate:dynamodb --file=2024_03_15_000001_create_ratings_and_reviews_table.php

# Fresh migrations (drops all tables and recreates them)
docker-compose exec app php artisan migrate:dynamodb --fresh
```

**Features:**
- ✅ **Sequential Execution**: Runs migrations in chronological order
- ✅ **Selective Migration**: Target specific migration files
- ✅ **Fresh Install**: Complete table recreation for clean environments
- ✅ **Error Handling**: Graceful handling of existing tables and conflicts
- ✅ **Logging**: Detailed output of migration progress

#### `make:dynamodb-migration`
Generate new DynamoDB migration files with proper structure and boilerplate code.

```bash
# Create a new DynamoDB migration
docker-compose exec app php artisan make:dynamodb-migration create_new_table

# The generated file will be in database/migrations_dynamodb/
# with proper timestamp and structure
```

**Generated Migration Structure:**
```php
<?php
use Illuminate\Database\Migrations\Migration;
use BaoPham\DynamoDb\DynamoDbClientService;

return new class extends Migration
{
    public function up(): void
    {
        $client = app(DynamoDbClientService::class)->getClient();
        // Your table creation logic here
    }

    public function down(): void
    {
        $client = app(DynamoDbClientService::class)->getClient();
        // Your table deletion logic here
    }
};
```

#### `make:dynamodb-model`
Generate DynamoDB model classes with proper configuration and relationships.

```bash
# Create a new DynamoDB model
docker-compose exec app php artisan make:dynamodb-model ProductReview

# Creates app/Models/ProductReview.php with DynamoDB-specific configuration
```

**Generated Model Features:**
- ✅ **DynamoDB Base Class**: Extends `DynamoDbModel` instead of Eloquent
- ✅ **Proper Configuration**: Pre-configured table name, primary keys, and fillable fields
- ✅ **Type Casting**: Automatic casting for DynamoDB data types
- ✅ **Relationships**: Support for DynamoDB-style relationships

### Business Logic Commands

#### `statistics:backfill`
Recalculate and update product statistics for existing reviews. Essential for data consistency and performance.

**Note**: This command uses a DynamoDB scan operation to find all unique product IDs, which may take time on large datasets but ensures complete coverage.

```bash
# Backfill statistics for all products (use with caution on large datasets)
docker-compose exec app php artisan statistics:backfill

# Backfill statistics for specific products (recommended for targeted updates)
docker-compose exec app php artisan statistics:backfill --product_id=product1 --product_id=product2 --product_id=product3

# Process with custom chunk size to optimize memory usage and scan performance
docker-compose exec app php artisan statistics:backfill --chunk_size=50

# Combine options for targeted processing
docker-compose exec app php artisan statistics:backfill --product_id=high_traffic_product --chunk_size=25
```

**Performance Tips:**
- 🎯 **Use specific product IDs** when possible to avoid full table scans
- ⚡ **Adjust chunk size** based on your DynamoDB capacity and memory constraints
- 📊 **Monitor progress** - the command shows real-time progress for both ID fetching and processing
- 🕐 **Run during low traffic** periods for large datasets

**Use Cases:**
- 🔄 **Data Migration**: After importing reviews from legacy systems
- 🐛 **Bug Fixes**: Correcting statistics after fixing calculation bugs
- 📊 **Performance Optimization**: Ensuring all products have pre-calculated statistics
- 🔧 **Maintenance**: Regular data consistency checks

#### `reviews:translate`
Batch translation of reviews using Google Translate API with intelligent caching and error handling.

```bash
# Translate published reviews (recommended for production)
docker-compose exec app php artisan reviews:translate --status=published --limit=100

# Translate pending reviews for moderation
docker-compose exec app php artisan reviews:translate --status=pending

# Translate all reviews (use carefully)
docker-compose exec app php artisan reviews:translate --limit=1000

# Translate specific language combinations
docker-compose exec app php artisan reviews:translate --from=en --to=ar --limit=50
```

**Smart Features:**
- 🧠 **Duplicate Detection**: Skips already translated content
- 💰 **Cost Optimization**: Batches API calls to reduce translation costs
- 🔄 **Resume Capability**: Can resume interrupted translation jobs
- 📝 **Progress Tracking**: Real-time progress updates and ETA

### Cache Management Commands

#### `cloudfront:invalidate`
Intelligent CloudFront cache invalidation with support for different invalidation strategies.

```bash
# Invalidate all cached content (use sparingly)
docker-compose exec app php artisan cloudfront:invalidate --all

# Invalidate specific review content
docker-compose exec app php artisan cloudfront:invalidate --review=review_id_123

# Invalidate product-specific API responses
docker-compose exec app php artisan cloudfront:invalidate --product=product_id_456

# Invalidate only media files
docker-compose exec app php artisan cloudfront:invalidate --media

# Invalidate only API responses
docker-compose exec app php artisan cloudfront:invalidate --api

# Synchronous invalidation (wait for completion)
docker-compose exec app php artisan cloudfront:invalidate --all --sync
```

## 💡 Why Use These Custom Commands?

### 🚀 **Developer Productivity**
- **Familiar Workflow**: Uses Laravel's familiar artisan command structure
- **Time Saving**: Automates complex DynamoDB operations that would require multiple AWS CLI commands
- **Error Prevention**: Built-in validation and error handling prevent common mistakes

### 🔧 **Operational Excellence**
- **Batch Processing**: Handles large datasets efficiently with memory management
- **Progress Tracking**: Real-time feedback on long-running operations
- **Rollback Support**: Safe migration practices with rollback capabilities

### 📊 **Data Integrity**
- **Consistency Checks**: Ensures data consistency across related tables
- **Validation**: Built-in validation for DynamoDB constraints and limits
- **Atomic Operations**: Uses DynamoDB's atomic operations where possible

### 🎯 **Best Practices**

**For Development:**
```bash
# Start fresh during development
docker-compose exec app php artisan migrate:dynamodb --fresh

# Generate new models with proper structure
docker-compose exec app php artisan make:dynamodb-model YourModel

# Test translations on small datasets
docker-compose exec app php artisan reviews:translate --limit=10
```

**For Production:**
```bash
# Run migrations safely
docker-compose exec app php artisan migrate:dynamodb

# Backfill statistics with optimized chunk size
docker-compose exec app php artisan statistics:backfill --chunk_size=50

# Translate content strategically
docker-compose exec app php artisan reviews:translate --status=published --limit=100
```

**For Maintenance:**
```bash
# Regular cache invalidation
docker-compose exec app php artisan cloudfront:invalidate --api

# Data consistency checks for specific products
docker-compose exec app php artisan statistics:backfill --product_id=high_traffic_product1 --product_id=high_traffic_product2
```

These custom commands are designed to make DynamoDB feel as natural as working with traditional relational databases while leveraging DynamoDB's unique strengths. They're essential tools for maintaining a healthy, performant ratings and reviews system.

## 🏗️ Architecture Overview

### System Architecture

```mermaid
graph TB
    Frontend[Frontend<br/>React/Vue] 
    LB[Load Balancer<br/>Optional]
    CDN[CloudFront<br/>CDN]
    
    Frontend <--> LB
    LB <--> CDN
    Frontend <--> Laravel
    
    subgraph "Laravel Application"
        Controllers[Controllers<br/>- Review<br/>- Product<br/>- Admin]
        Services[Services<br/>- Media<br/>- Translation<br/>- CloudFront<br/>- Statistics]
        Jobs[Jobs<br/>- Statistics<br/>- Cache<br/>- Translation]
        
        Controllers --> Services
        Services --> Jobs
    end
    
    Laravel --> DynamoDB[(DynamoDB<br/>- Reviews<br/>- Statistics<br/>- Media Meta)]
    Laravel --> MySQL[(MySQL<br/>- Sessions<br/>- Jobs<br/>- Migrations)]
    Laravel --> Redis[(Redis<br/>- Cache<br/>- Queues<br/>- Sessions)]
    
    Laravel --> S3[S3 Storage<br/>- Media Files<br/>- Images<br/>- Videos]
    Laravel --> Translate[Google Translate<br/>- Auto Trans<br/>- On-demand<br/>- Batch]
    Laravel --> Horizon[Horizon<br/>- Queue Mon<br/>- Job Status<br/>- Metrics]
    
    CDN --> S3
```

### Data Flow Architecture

```mermaid
graph TD
    Request[API Request] --> Controller[Controller<br/>Validation & Authorization]
    
    Controller --> Service[Service Layer<br/>Business Logic]
    
    subgraph "Services"
        MediaService[MediaUploadService]
        TransService[TranslationService]
        StatsService[StatisticsService]
        CloudService[CloudFrontService]
    end
    
    Service --> MediaService
    Service --> TransService
    Service --> StatsService
    Service --> CloudService
    
    Service --> Model[Model Layer<br/>Data Access]
    
    subgraph "Models"
        ReviewModel[RatingAndReview]
        StatsModel[RatingsAndReviewStatistics]
    end
    
    Model --> ReviewModel
    Model --> StatsModel
    
    ReviewModel --> DynamoDB[(DynamoDB)]
    StatsModel --> DynamoDB
    
    Service --> Queue[Queue Jobs<br/>Background Processing]
    
    subgraph "Background Jobs"
        StatsJob[UpdateProductStatisticsJob]
        CacheJob[InvalidateCloudFrontCache]
    end
    
    Queue --> StatsJob
    Queue --> CacheJob
    
    StatsJob --> Redis[(Redis)]
    CacheJob --> Redis
    
    Service --> External[External Services]
    
    subgraph "Third-party APIs"
        GoogleAPI[Google Translate API]
        S3API[AWS S3 & CloudFront]
    end
    
    External --> GoogleAPI
    External --> S3API
```

### Database Relationships

```mermaid
erDiagram
    RATINGS_AND_REVIEWS {
        string product_id PK
        string review_id PK
        string user_id
        number rating
        string title
        string content
        string language
        map translated_content
        list media
        string publication_status
        string country
        string created_at
        string updated_at
    }
    
    RATINGS_AND_REVIEW_STATISTICS {
        string product_id PK
        number rating_count
        number average_rating
        map rating_distribution
        map percentage_distribution
        string last_calculated_at
    }
    
    RATINGS_AND_REVIEWS ||--o{ RATINGS_AND_REVIEW_STATISTICS : "aggregates to"
```

### Queue Processing Flow

```mermaid
graph LR
    subgraph "API Actions"
        Create[Create Review]
        Update[Update Review]
        Delete[Delete Review]
        Publish[Publish Review]
    end
    
    Create --> StatsQueue[Statistics Queue]
    Update --> StatsQueue
    Delete --> StatsQueue
    Publish --> StatsQueue
    
    Create --> CacheQueue[Cache Queue]
    Update --> CacheQueue
    Delete --> CacheQueue
    
    Create --> TransQueue[Translation Queue]
    Update --> TransQueue
    
    subgraph "Background Processing"
        StatsQueue --> StatsWorker[Statistics Worker]
        CacheQueue --> CacheWorker[Cache Worker]
        TransQueue --> TransWorker[Translation Worker]
    end
    
    StatsWorker --> UpdateStats[Update Product Statistics]
    CacheWorker --> InvalidateCache[Invalidate CloudFront]
    TransWorker --> Translate[Translate Content]
    
    UpdateStats --> DynamoDB[(DynamoDB)]
    InvalidateCache --> CloudFront[CloudFront API]
    Translate --> GoogleAPI[Google Translate API]
    
    subgraph "Monitoring"
        Horizon[Laravel Horizon]
    end
    
    StatsWorker --> Horizon
    CacheWorker --> Horizon
    TransWorker --> Horizon
```

### Database Schema

#### DynamoDB Tables

**ratings_and_reviews**
```
Primary Key: product_id (Hash), review_id (Range)
Attributes:
- product_id (String)
- review_id (String)
- user_id (String)
- rating (Number)
- title (String)
- content (String)
- language (String)
- translated_content (Map)
- media (List)
- publication_status (String)
- country (String)
- created_at (String)
- updated_at (String)

Global Secondary Indexes:
- product_id-index: product_id (Hash)
- user_id-index: user_id (Hash)
- publication_status-index: publication_status (Hash)
```

**ratings_and_review_statistics**
```
Primary Key: product_id (Hash)
Attributes:
- product_id (String)
- rating_count (Number)
- average_rating (Number)
- rating_distribution (Map) # {1: count, 2: count, ...}
- percentage_distribution (Map) # {1: percentage, 2: percentage, ...}
- last_calculated_at (String)
```

#### MySQL Tables

- **migrations**: Laravel migration tracking
- **jobs**: Queue job storage
- **job_batches**: Batch job tracking
- **failed_jobs**: Failed job storage
- **cache**: Application cache
- **cache_locks**: Cache locking
- **sessions**: User sessions

### Service Layer Architecture

#### MediaUploadService
- Handles file uploads to various storage backends
- Validates file types and sizes
- Generates secure URLs for media access
- Supports local, public, and S3 storage

#### TranslationService
- Integrates with Google Translate API
- Manages automatic and on-demand translations
- Caches translations to avoid duplicate API calls
- Supports batch translation processing

#### RatingsAndReviewsStatisticsService
- Calculates real-time product statistics
- Handles pagination for large datasets
- Queues background statistics updates
- Maintains data consistency

#### CloudFrontService
- Manages CloudFront cache invalidation
- Handles both media and API response invalidation
- Supports batch and individual invalidations
- Provides synchronous and asynchronous operations

### Queue Architecture

The application uses Redis-based queues with Laravel Horizon for monitoring:

**Queue Types:**
- `default`: General background tasks
- `statistics`: Product statistics calculations
- `translations`: Review translation processing
- `cache`: CloudFront cache invalidation

**Job Processing:**
- Jobs are processed asynchronously
- Failed jobs are retried with exponential backoff
- Horizon provides real-time monitoring and metrics
- Dead letter queue for permanently failed jobs

### Security Considerations

- **Input Validation**: All API inputs are validated using Laravel Form Requests
- **File Upload Security**: File types and sizes are strictly validated
- **SQL Injection Prevention**: Using Eloquent ORM and parameterized queries
- **NoSQL Injection Prevention**: DynamoDB queries use proper parameter binding
- **Rate Limiting**: API endpoints can be rate-limited (configure in RouteServiceProvider)
- **CORS**: Properly configured for cross-origin requests
- **Environment Variables**: Sensitive data stored in environment variables

### Performance Optimizations

- **Pre-calculated Statistics**: Product ratings are pre-calculated and cached
- **DynamoDB Indexes**: Optimized for common query patterns
- **Redis Caching**: Frequently accessed data is cached
- **CloudFront CDN**: Media files and API responses are cached at edge locations
- **Queue Processing**: Heavy operations are processed in background
- **Pagination**: Large datasets are paginated to prevent memory issues

### Monitoring & Observability

- **Horizon Dashboard**: Real-time queue monitoring
- **Laravel Logs**: Comprehensive application logging
- **DynamoDB Admin**: Database inspection and management
- **Redis Commander**: Cache and queue inspection
- **phpMyAdmin**: MySQL database management

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
docker-compose exec app php artisan test

# Run specific test file
docker-compose exec app php artisan test tests/Feature/ReviewControllerTest.php

# Run with coverage
docker-compose exec app php artisan test --coverage
```

## 🚀 Deployment

### Production Environment Variables

```env
APP_ENV=production
APP_DEBUG=false
QUEUE_CONNECTION=redis
CACHE_DRIVER=redis
SESSION_DRIVER=redis

# Use real AWS credentials
AWS_ACCESS_KEY_ID=your-real-access-key
AWS_SECRET_ACCESS_KEY=your-real-secret-key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=your-production-bucket

# Use real DynamoDB
DYNAMODB_CONNECTION=aws
DYNAMODB_ENDPOINT=  # Leave empty for real DynamoDB

# CloudFront
CLOUDFRONT_DISTRIBUTION_ID=your-production-distribution-id
AWS_URL=https://your-production-cloudfront-domain.cloudfront.net
```

### Docker Production Build

```bash
# Build production image
docker build -f docker/application/Dockerfile.prod -t ratings-reviews-service .

# Run with production environment
docker-compose -f docker-compose.prod.yml up -d
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Troubleshooting

### Common Issues

**DynamoDB Connection Issues:**
```bash
# Check if DynamoDB container is running
docker-compose ps dynamodb

# Check DynamoDB logs
docker-compose logs dynamodb

# Restart DynamoDB
docker-compose restart dynamodb
```

**Queue Jobs Not Processing:**
```bash
# Check Horizon status
docker-compose exec app php artisan horizon:status

# Restart Horizon
docker-compose restart horizon

# Check Redis connection
docker-compose exec app php artisan tinker
>>> Redis::ping()
```

**Media Upload Issues:**
```bash
# Check storage permissions
docker-compose exec app ls -la storage/app/

# Create storage link
docker-compose exec app php artisan storage:link

# Check disk space
docker-compose exec app df -h
```

### Performance Tuning

**DynamoDB Performance:**
- Use appropriate indexes for your query patterns
- Consider using batch operations for bulk data
- Monitor read/write capacity units

**Redis Performance:**
- Monitor memory usage
- Configure appropriate eviction policies
- Use Redis clustering for high availability

**Application Performance:**
- Enable OPcache in production
- Use Laravel Octane for improved performance
- Implement proper caching strategies

## 📞 Support

For support and questions:
- Create an issue in the repository
- Check the [API documentation](docs/API.md)
- Review the [troubleshooting section](#-troubleshooting)

---

**Built with ❤️ for Mumzworld**

###
