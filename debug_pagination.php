<?php

/**
 * Debug script for testing cursor-based pagination
 * 
 * Usage:
 * 1. First request: php debug_pagination.php
 * 2. Subsequent requests: php debug_pagination.php "<cursor_from_previous_response>"
 */

$baseUrl = 'http://localhost:7001/api/reviews';
$perPage = 2;

// Get cursor from command line argument
$cursor = $argv[1] ?? null;

// Build the URL
$params = ['per_page' => $perPage];
if ($cursor) {
    $params['cursor'] = $cursor;
}

$url = $baseUrl . '?' . http_build_query($params);

echo "Testing pagination:\n";
echo "URL: $url\n";
echo "Cursor: " . ($cursor ? $cursor : 'none') . "\n";
echo str_repeat('-', 80) . "\n";

// Make the request
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: application/json']);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode !== 200) {
    echo "Error: HTTP $httpCode\n";
    echo "Response: $response\n";
    exit(1);
}

$data = json_decode($response, true);

if (!$data) {
    echo "Error: Invalid JSON response\n";
    echo "Response: $response\n";
    exit(1);
}

// Display results
echo "Results found: " . count($data['data'] ?? []) . "\n";
echo "Has more: " . ($data['pagination']['has_more'] ? 'yes' : 'no') . "\n";
echo "Next cursor: " . ($data['pagination']['cursor'] ?? 'none') . "\n";

echo "\nReview IDs in this page:\n";
foreach ($data['data'] ?? [] as $review) {
    echo "- {$review['review_id']}\n";
}

if ($data['pagination']['cursor'] ?? null) {
    echo "\nNext command to run:\n";
    echo "php debug_pagination.php \"{$data['pagination']['cursor']}\"\n";

    // Decode cursor to show what's inside
    $decodedCursor = json_decode(base64_decode($data['pagination']['cursor']), true);
    echo "\nCursor contains:\n";
    foreach ($decodedCursor as $key => $value) {
        echo "- $key: $value\n";
    }
}

echo "\n" . str_repeat('-', 80) . "\n";
