openapi: 3.1.0
info:
  title: Mumzworld Ratings and Reviews API
  description: API for managing product ratings and reviews, including support for media uploads and translations.
  version: 1.0.0
  contact:
    name: Mumzworld
servers:
  - url: /api
    description: API base URL

paths:
  /reviews:
    post:
      summary: Create a new review
      description: Creates a new rating and review for a product with optional media files.
      operationId: storeReview
      tags:
        - Reviews
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CreateReviewRequest'
      responses:
        '200':
          description: Review created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReviewResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'

    get:
      summary: Get reviews by status
      description: Retrieves reviews filtered by publication status and other criteria. Uses DynamoDB key-based pagination with next_token for efficient retrieval of large result sets.
      operationId: getReviewsByStatus
      tags:
        - Reviews
      parameters:
        - name: publication_status
          in: query
          required: false
          description: Filter by publication status
          schema:
            type: string
            enum: [pending, published, rejected]
        - name: country
          in: query
          required: false
          description: Filter by 2-letter country code
          schema:
            type: string
            minLength: 2
            maxLength: 2
        - name: language
          in: query
          required: false
          description: Filter by original language
          schema:
            type: string
            enum: [en, ar]
        - name: per_page
          in: query
          required: false
          description: Number of reviews per page (default 15, max 100)
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 15
        - name: page
          in: query
          required: false
          description: Page number for pagination (deprecated, use next_token for DynamoDB pagination)
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: next_token
          in: query
          required: false
          description: Token for DynamoDB key-based pagination. Use the next_token from the previous response to get the next page of results.
          schema:
            type: string
        - name: invalidate_cache
          in: query
          required: false
          description: Set to true to invalidate the CloudFront cache for this endpoint
          schema:
            type: boolean
            default: false
      responses:
        '200':
          description: Reviews retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Review'
                  links:
                    $ref: '#/components/schemas/PaginationLinks'
                  meta:
                    $ref: '#/components/schemas/PaginationMeta'
                  next_token:
                    type: string
                    nullable: true
                    description: Token for retrieving the next page of results using DynamoDB key-based pagination
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'

  /products/{id}/reviews:
    get:
      summary: Get product reviews
      description: Retrieves all reviews for a specific product with optional filtering.
      operationId: getProductReviews
      tags:
        - Reviews
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
        - name: country
          in: query
          required: false
          description: Filter reviews by country code (2 letters)
          schema:
            type: string
            minLength: 2
            maxLength: 2
        - name: language
          in: query
          required: false
          description: Filter reviews by language
          schema:
            type: string
            enum: [en, ar]
        - name: user_id
          in: query
          required: false
          description: Filter reviews by user ID
          schema:
            type: string
        - name: publication_status
          in: query
          required: false
          description: Filter reviews by publication status
          schema:
            type: string
            enum: [pending, published, rejected, all]
            default: published
      responses:
        '200':
          description: List of reviews for the product
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Review'
                  rating_summary:
                    type: object
                    description: Pre-calculated rating summary for the product, based on published reviews. Updated automatically when reviews change.
                    properties:
                      average:
                        type: number
                        format: float
                        description: Average rating value (1-5). Returns 0 if no published reviews.
                        example: 4.5
                      count:
                        type: integer
                        description: Total number of published reviews. Returns 0 if no published reviews.
                        example: 10
                      distribution:
                        type: object
                        description: Distribution of ratings by value
                        properties:
                          "1":
                            type: integer
                            example: 0
                          "2":
                            type: integer
                            example: 1
                          "3":
                            type: integer
                            example: 1
                          "4":
                            type: integer
                            example: 2
                          "5":
                            type: integer
                            example: 6
                      percentage_distribution:
                        type: object
                        description: Percentage distribution of ratings by value
                        properties:
                          "1":
                            type: number
                            format: float
                            example: 0
                          "2":
                            type: number
                            format: float
                            example: 10
                          "3":
                            type: number
                            format: float
                            example: 10
                          "4":
                            type: number
                            format: float
                            example: 20
                          "5":
                            type: number
                            format: float
                            example: 60

  /products/{id}/rating:
    get:
      summary: Get product rating summary
      description: Retrieves the pre-calculated rating summary for a specific product. Statistics are based on published reviews and updated automatically.
      operationId: getProductRatingSummary
      tags:
        - Reviews
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
      responses:
        '200':
          description: Pre-calculated rating summary for the product. Returns a zeroed summary if the product has no published reviews or statistics are not yet generated.
          content:
            application/json:
              schema:
                type: object
                properties:
                  average:
                    type: number
                    format: float
                    description: Average rating value (1-5) from pre-calculated statistics. Returns 0 if no published reviews.
                    example: 4.5
                  count:
                    type: integer
                    description: Total number of published reviews from pre-calculated statistics. Returns 0 if no published reviews.
                    example: 10
                  distribution:
                    type: object
                    description: Distribution of ratings by value
                    properties:
                      "1":
                        type: integer
                        example: 0
                      "2":
                        type: integer
                        example: 1
                      "3":
                        type: integer
                        example: 1
                      "4":
                        type: integer
                        example: 2
                      "5":
                        type: integer
                        example: 6
                  percentage_distribution:
                    type: object
                    description: Percentage distribution of ratings by value
                    properties:
                      "1":
                        type: number
                        format: float
                        example: 0
                      "2":
                        type: number
                        format: float
                        example: 10
                      "3":
                        type: number
                        format: float
                        example: 10
                      "4":
                        type: number
                        format: float
                        example: 20
                      "5":
                        type: number
                        format: float
                        example: 60

  /reviews/{id}:
    delete:
      summary: Delete a review
      description: Deletes a specific review and invalidates related caches.
      operationId: destroyReview
      tags:
        - Reviews
      parameters:
        - name: id
          in: path
          required: true
          description: Review ID
          schema:
            type: string
      responses:
        '200':
          description: Review deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Review deleted successfully
        '404':
          description: Review not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Review not found

  /reviews/{id}/publication:
    put:
      summary: Update publication status
      description: Updates the publication status of a specific review.
      operationId: updatePublicationStatus
      tags:
        - Reviews
      parameters:
        - name: id
          in: path
          required: true
          description: Review ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - publication_status
              properties:
                publication_status:
                  type: string
                  enum: [pending, published, rejected]
      responses:
        '200':
          description: Publication status updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReviewResponse'
        '404':
          description: Review not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Review not found
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'

  /reviews/{id}/translate:
    get:
      summary: Get translated review
      description: Retrieves a review with translation to the requested language. If the translation doesn't exist, it will be created on-demand.
      operationId: getTranslatedReview
      tags:
        - Reviews
      parameters:
        - name: id
          in: path
          required: true
          description: Review ID
          schema:
            type: string
        - name: language
          in: query
          required: true
          description: Target language code
          schema:
            type: string
            enum: [en, ar]
      responses:
        '200':
          description: Review with translation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReviewResponse'
        '404':
          description: Review not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Review not found
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '500':
          description: Translation failed
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Translation failed
                  error:
                    type: string

  /reviews/pending-check:
    get:
      summary: Check if there are pending reviews
      description: Checks if there are any reviews with pending publication status.
      operationId: hasPendingReviews
      tags:
        - Reviews
      responses:
        '200':
          description: Pending reviews check result
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      has_pending_reviews:
                        type: boolean
                        description: Whether there are any pending reviews
                        example: true
                  meta:
                    type: object
                    properties:
                      timestamp:
                        type: string
                        format: date-time
                        description: Timestamp when the check was performed
                        example: "2024-03-15T12:34:56Z"

components:
  schemas:
    CreateReviewRequest:
      type: object
      required:
        - user_id
        - product_id
        - rating
        - original_language
        - country
      properties:
        user_id:
          type: string
          maxLength: 255
          description: ID of the user creating the review
        product_id:
          type: string
          maxLength: 255
          description: ID of the product being reviewed
        rating:
          type: integer
          minimum: 1
          maximum: 5
          description: Rating value (1-5)
        original_language:
          type: string
          enum: [en, ar]
          description: Original language of the review
        review_en:
          type: string
          maxLength: 1000
          description: Review text in English (required if original_language is en)
        review_ar:
          type: string
          maxLength: 1000
          description: Review text in Arabic (required if original_language is ar)
        country:
          type: string
          minLength: 2
          maxLength: 2
          description: 2-letter country code
        media_files:
          type: array
          description: Media files to attach to the review (images and videos)
          items:
            type: string
            format: binary

    Review:
      type: object
      properties:
        review_id:
          type: string
          description: Unique identifier for the review
        user_id:
          type: string
          description: ID of the user who created the review
        product_id:
          type: string
          description: ID of the product being reviewed
        rating:
          type: integer
          minimum: 1
          maximum: 5
          description: Rating value (1-5)
        original_language:
          type: string
          enum: [en, ar]
          description: Original language of the review
        review_en:
          type: string
          description: Review text in English
        review_ar:
          type: string
          description: Review text in Arabic
        country:
          type: string
          description: 2-letter country code
        publication_status:
          type: string
          enum: [pending, published, rejected]
          description: Publication status of the review
        created_at:
          type: string
          format: date-time
          description: Creation timestamp
        media:
          type: array
          description: Media attachments
          items:
            $ref: '#/components/schemas/Media'

    Media:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the media item
        type:
          type: string
          enum: [image, video]
          description: Type of media
        path:
          type: string
          description: Storage path
        url:
          type: string
          description: Publicly accessible URL for the media item. The specific format (e.g., direct S3, CloudFront CDN, or local path) depends on the server's storage configuration. See the main API documentation under 'Media Storage' for details on S3/CloudFront URL behavior.

    ReviewResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/Review'

    ValidationError:
      type: object
      properties:
        message:
          type: string
          example: The given data was invalid.
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
          example:
            rating: ["The rating field is required."]
            original_language: ["The original language field must be either 'en' or 'ar'."]

    PaginationLinks:
      type: object
      properties:
        first:
          type: string
          format: uri
          description: URL for the first page
        last:
          type: string
          format: uri
          description: URL for the last page
        prev:
          type: string
          format: uri
          nullable: true
          description: URL for the previous page, null if on first page
        next:
          type: string
          format: uri
          nullable: true
          description: URL for the next page, null if on last page

    PaginationMeta:
      type: object
      properties:
        current_page:
          type: integer
          description: Current page number
        from:
          type: integer
          description: Index of the first item on the current page
        last_page:
          type: integer
          description: Last page number
        links:
          type: array
          description: Array of pagination links
          items:
            type: object
            properties:
              url:
                type: string
                format: uri
                nullable: true
                description: URL for the page
              label:
                type: string
                description: Label for the page (number or text)
              active:
                type: boolean
                description: Whether this is the current page
        path:
          type: string
          format: uri
          description: Base path for pagination URLs
        per_page:
          type: integer
          description: Number of items per page
        to:
          type: integer
          description: Index of the last item on the current page
        total:
          type: integer
          description: Total number of items 