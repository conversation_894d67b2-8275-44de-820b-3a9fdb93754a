<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

/**
 * Service for handling cursor-based pagination with encoded tokens.
 * 
 * This service provides methods to encode and decode pagination cursors
 * for DynamoDB queries, making the pagination tokens user-friendly and
 * hiding internal implementation details.
 */
class CursorPaginationService
{
    /**
     * Encode a DynamoDB ExclusiveStartKey into a base64 cursor token.
     *
     * @param array $exclusiveStartKey The DynamoDB ExclusiveStartKey array
     * @return string Base64 encoded cursor token
     */
    public function encodeCursor(array $exclusiveStartKey): string
    {
        try {
            $json = json_encode($exclusiveStartKey, JSON_THROW_ON_ERROR);
            $cursor = base64_encode($json);

            // Log the encoded cursor for debugging
            if (function_exists('app') && app()->bound('log')) {
                Log::debug('CursorPaginationService: Encoded cursor', [
                    'exclusive_start_key' => $exclusiveStartKey,
                    'cursor' => $cursor
                ]);
            }

            return $cursor;
        } catch (\JsonException $e) {
            // Only log if we're in a Laravel application context
            if (function_exists('app') && app()->bound('log')) {
                Log::error('Failed to encode cursor', [
                    'exclusive_start_key' => $exclusiveStartKey,
                    'error' => $e->getMessage()
                ]);
            }
            throw new \InvalidArgumentException('Failed to encode cursor: ' . $e->getMessage());
        }
    }

    /**
     * Decode a base64 cursor token back into a DynamoDB ExclusiveStartKey.
     *
     * @param string $cursor Base64 encoded cursor token
     * @return array DynamoDB ExclusiveStartKey array
     * @throws \InvalidArgumentException If the cursor is invalid
     */
    public function decodeCursor(string $cursor): array
    {
        try {
            $decoded = base64_decode($cursor, true);

            if ($decoded === false) {
                throw new \InvalidArgumentException('Invalid base64 cursor');
            }

            $exclusiveStartKey = json_decode($decoded, true, 512, JSON_THROW_ON_ERROR);

            if (!is_array($exclusiveStartKey)) {
                throw new \InvalidArgumentException('Cursor does not contain valid array data');
            }

            // Log the decoded cursor for debugging
            if (function_exists('app') && app()->bound('log')) {
                Log::debug('CursorPaginationService: Decoded cursor', [
                    'cursor' => $cursor,
                    'decoded' => $exclusiveStartKey
                ]);
            }

            return $exclusiveStartKey;
        } catch (\JsonException $e) {
            // Only log if we're in a Laravel application context
            if (function_exists('app') && app()->bound('log')) {
                Log::warning('Failed to decode cursor', [
                    'cursor' => $cursor,
                    'error' => $e->getMessage()
                ]);
            }
            throw new \InvalidArgumentException('Invalid cursor format: ' . $e->getMessage());
        }
    }

    /**
     * Validate that a cursor string is properly formatted.
     *
     * @param string $cursor The cursor to validate
     * @return bool True if valid, false otherwise
     */
    public function isValidCursor(string $cursor): bool
    {
        try {
            $this->decodeCursor($cursor);
            return true;
        } catch (\InvalidArgumentException) {
            return false;
        }
    }

    /**
     * Create a pagination response structure with cursor and has_more flag.
     *
     * @param array $data The data array to paginate
     * @param array|null $lastEvaluatedKey The DynamoDB LastEvaluatedKey
     * @param int $requestedLimit The number of items requested
     * @return array Pagination response structure
     */
    public function createPaginationResponse(array $data, ?array $lastEvaluatedKey, int $requestedLimit): array
    {
        $response = [
            'data' => $data,
            'pagination' => [
                'has_more' => false,
                'cursor' => null
            ]
        ];

        // If we have a LastEvaluatedKey and we got the full requested amount,
        // there might be more data available
        if ($lastEvaluatedKey && count($data) >= $requestedLimit) {
            $response['pagination']['has_more'] = true;
            $response['pagination']['cursor'] = $this->encodeCursor($lastEvaluatedKey);
        }

        return $response;
    }

    /**
     * Extract cursor information for debugging purposes.
     *
     * @param string $cursor Base64 encoded cursor
     * @return array Cursor information for debugging
     */
    public function debugCursor(string $cursor): array
    {
        try {
            $decoded = $this->decodeCursor($cursor);
            return [
                'valid' => true,
                'decoded_data' => $decoded,
                'keys' => array_keys($decoded),
                'cursor_length' => strlen($cursor)
            ];
        } catch (\InvalidArgumentException $e) {
            return [
                'valid' => false,
                'error' => $e->getMessage(),
                'cursor_length' => strlen($cursor)
            ];
        }
    }
}
