<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\DeleteRatingAndReviewRequest;
use App\Http\Requests\StoreRatingAndReviewRequest;
use App\Http\Requests\UpdatePublicationStatusRequest;
use App\Http\Requests\GetReviewsByStatusRequest;
use App\Http\Resources\RatingAndReviewResource;
use App\Models\RatingAndReview;
use App\Services\CloudFrontService;
use App\Services\CursorPaginationService;
use App\Services\MediaUploadService;
use App\Services\RatingsAndReviewsStatisticsService;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\JsonResponse;

/**
 * @OA\Tag(
 *     name="Reviews",
 *     description="API Endpoints for managing reviews"
 * )
 */
class ReviewController extends Controller
{
    protected MediaUploadService $mediaUploadService;
    protected CloudFrontService $cloudFrontService;
    protected RatingsAndReviewsStatisticsService $ratingsAndReviewsStatisticsService;
    protected CursorPaginationService $cursorPaginationService;

    /**
     * Create a new controller instance.
     *
     * @param MediaUploadService $mediaUploadService Service for handling media uploads.
     * @param CloudFrontService $cloudFrontService Service for CloudFront cache invalidations.
     * @param RatingsAndReviewsStatisticsService $ratingsAndReviewsStatisticsService Service for managing review statistics.
     * @param CursorPaginationService $cursorPaginationService Service for handling cursor-based pagination.
     */
    public function __construct(
        MediaUploadService $mediaUploadService,
        CloudFrontService $cloudFrontService,
        RatingsAndReviewsStatisticsService $ratingsAndReviewsStatisticsService,
        CursorPaginationService $cursorPaginationService
    ) {
        $this->mediaUploadService = $mediaUploadService;
        $this->cloudFrontService = $cloudFrontService;
        $this->ratingsAndReviewsStatisticsService = $ratingsAndReviewsStatisticsService;
        $this->cursorPaginationService = $cursorPaginationService;
    }

    /**
     * @OA\Post(
     *     path="/reviews",
     *     summary="Create a new review",
     *     description="Creates a new rating and review for a product with optional media files.",
     *     operationId="storeReview",
     *     tags={"Reviews"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 required={"user_id", "product_id", "rating", "original_language", "country"},
     *                 @OA\Property(property="user_id", type="string", maxLength=255, description="ID of the user creating the review"),
     *                 @OA\Property(property="product_id", type="string", maxLength=255, description="ID of the product being reviewed"),
     *                 @OA\Property(property="rating", type="integer", minimum=1, maximum=5, description="Rating value (1-5)"),
     *                 @OA\Property(property="original_language", type="string", enum={"en", "ar"}, description="Original language of the review"),
     *                 @OA\Property(property="review_en", type="string", maxLength=1000, description="Review text in English (required if original_language is en)"),
     *                 @OA\Property(property="review_ar", type="string", maxLength=1000, description="Review text in Arabic (required if original_language is ar)"),
     *                 @OA\Property(property="country", type="string", minLength=2, maxLength=2, description="2-letter country code"),
     *                 @OA\Property(property="media_files", type="array", @OA\Items(type="string", format="binary"), description="Media files to attach to the review")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Review created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="review_id", type="string"),
     *                 @OA\Property(property="user_id", type="string"),
     *                 @OA\Property(property="product_id", type="string"),
     *                 @OA\Property(property="rating", type="integer"),
     *                 @OA\Property(property="original_language", type="string"),
     *                 @OA\Property(property="review_en", type="string"),
     *                 @OA\Property(property="review_ar", type="string"),
     *                 @OA\Property(property="country", type="string"),
     *                 @OA\Property(property="publication_status", type="string"),
     *                 @OA\Property(property="created_at", type="string", format="date-time"),
     *                 @OA\Property(property="media", type="array", @OA\Items(type="object"))
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error"
     *     )
     * )
     */
    public function store(StoreRatingAndReviewRequest $request): RatingAndReviewResource
    {
        $validatedData = $request->validated();
        if (isset($validatedData['media_files'])) {
            unset($validatedData['media_files']);
        }
        $review = new RatingAndReview($validatedData);
        $review->save();
        if ($request->hasFile('media_files')) {
            $mediaItems = [];
            foreach ($request->file('media_files') as $file) {
                $mediaItems[] = $this->mediaUploadService->uploadMedia($file, $review->review_id);
            }
            $review->media = $mediaItems;
            $review->save();
        }
        $this->cloudFrontService->invalidateProductReviewsApi($review->product_id);
        return new RatingAndReviewResource($review);
    }

    /**
     * Remove the specified review from storage.
     *
     * If the deleted review was published, a job is dispatched to update product statistics.
     *
     * @param DeleteRatingAndReviewRequest $request
     * @param string $reviewId Review ID to delete.
     * @return JsonResponse
     */
    public function destroy(DeleteRatingAndReviewRequest $request, string $reviewId): JsonResponse
    {
        // Validate reviewId format
        $this->validateReviewId($reviewId);

        $review = RatingAndReview::find($reviewId);
        if (!$review) {
            return response()->json(['message' => 'Review not found'], Response::HTTP_NOT_FOUND);
        }
        $productIdToUpdate = $review->product_id;
        $wasPublished = $review->publication_status === 'published';
        $hasMedia = !empty($review->media);
        $mediaItems = $hasMedia ? $review->media : [];
        $review->delete();
        if ($wasPublished) {
            $this->ratingsAndReviewsStatisticsService->queueStatsRecalculation($productIdToUpdate);
        }
        if ($hasMedia) {
            $this->cloudFrontService->invalidateMediaItems($mediaItems);
        }
        $this->cloudFrontService->invalidateReviewApi($reviewId);
        $this->cloudFrontService->invalidateProductReviewsApi($productIdToUpdate);
        return response()->json(['message' => 'Review deleted successfully'], Response::HTTP_OK);
    }

    /**
     * Update the publication status of the specified review.
     *
     * Dispatches a job to update product statistics after changing the status.
     *
     * @param UpdatePublicationStatusRequest $request
     * @param string $reviewId Review ID.
     * @return RatingAndReviewResource|JsonResponse
     */
    public function updatePublicationStatus(UpdatePublicationStatusRequest $request, string $reviewId): RatingAndReviewResource|JsonResponse
    {
        try {
            // Validate reviewId format
            $this->validateReviewId($reviewId);

            $review = RatingAndReview::find($reviewId);
            if (!$review) {
                return response()->json(['message' => 'Review not found'], Response::HTTP_NOT_FOUND);
            }
            $review->publication_status = $request->publication_status;
            $review->save();
            $this->ratingsAndReviewsStatisticsService->queueStatsRecalculation($review->product_id);
            try {
                $this->cloudFrontService->invalidateReviewApi($reviewId);
                $this->cloudFrontService->invalidateProductReviewsApi($review->product_id);
            } catch (\Exception $e) {
                Log::error('Cache invalidation failed during publication status update', [
                    'review_id' => $reviewId,
                    'error' => $e->getMessage()
                ]);
            }
            return new RatingAndReviewResource($review);
        } catch (\Exception $e) {
            Log::error('Failed to update publication status', [
                'review_id' => $reviewId,
                'status' => $request->publication_status,
                'error' => $e->getMessage(),
                'trace' => substr($e->getTraceAsString(), 0, 500)
            ]);
            return response()->json([
                'message' => 'Failed to update publication status',
                'error' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get reviews filtered by status and other criteria, with pagination.
     *
     * @param GetReviewsByStatusRequest $request Filter and pagination parameters
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function getReviewsByStatus(GetReviewsByStatusRequest $request): \Illuminate\Http\Resources\Json\AnonymousResourceCollection
    {
        // Get validated and normalized parameters
        $validated = $request->validated();

        // Extract primary filter parameters
        $publicationStatus = $validated['publication_status'] ?? null;
        $userId = $validated['user_id'] ?? null;
        $productId = $validated['product_id'] ?? null;
        $rating = $validated['rating'] ?? null;
        $query = RatingAndReview::query();

        // Select appropriate GSI based on primary filter
        if ($publicationStatus) {
            $query->where('publication_status', $publicationStatus)
                ->usingIndex('publication_status-index');
        } elseif ($userId) {
            $query->where('user_id', $userId)
                ->usingIndex('user_id-index');
        } elseif ($productId) {
            $query->where('product_id', $productId)
                ->usingIndex('product_id-index');
        } else {
            // Fallback to product index if no primary filter provided
            Log::debug("[Controller] getReviewsByStatus called without primary indexed filter, defaulting to product_id-index scan for pagination base.");
            $query->usingIndex('product_id-index');
        }

        // Apply secondary filters
        if (isset($validated['country'])) {
            $query->where('country', $validated['country']);
        }
        if (isset($validated['language'])) {
            $query->where('original_language', $validated['language']);
        }
        if ($rating !== null) {
            $query->where('rating', $rating);
        }

        // Setup pagination limits
        $perPage = (int) $request->input('per_page', 100);
        $perPage = min($perPage, 100); // Cap max results at 100
        $query->limit($perPage);

        // Handle DynamoDB key-based pagination token (cursor only)
        $lastEvaluatedKey = null;
        if ($request->has('cursor') && !empty($request->cursor)) {
            try {
                $lastEvaluatedKey = $this->cursorPaginationService->decodeCursor($request->cursor);
                Log::debug('[Controller] Using cursor pagination', [
                    'cursor' => $request->cursor,
                    'decoded_key' => $lastEvaluatedKey
                ]);
            } catch (\Exception $e) {
                Log::error('[Controller] Failed to decode cursor', [
                    'cursor' => $request->cursor,
                    'error' => $e->getMessage()
                ]);
            }
        }

        if ($lastEvaluatedKey) {
            // Always include both the GSI hash key and the table's primary key (review_id)
            $validatedKey = ['review_id' => $lastEvaluatedKey['review_id'] ?? null];

            if ($publicationStatus) {
                $validatedKey['publication_status'] = $lastEvaluatedKey['publication_status'] ?? $publicationStatus;
            } elseif ($userId) {
                $validatedKey['user_id'] = $lastEvaluatedKey['user_id'] ?? $userId;
            } elseif ($productId) {
                $validatedKey['product_id'] = $lastEvaluatedKey['product_id'] ?? $productId;
            } else {
                // Default case: ensure we have product_id for the default index
                $validatedKey['product_id'] = $lastEvaluatedKey['product_id'] ?? null;
            }

            // Remove any null values
            $validatedKey = array_filter($validatedKey, function ($value) {
                return $value !== null;
            });

            // Only proceed if both the GSI hash key and review_id are present
            $hasGsiKey = false;
            if ($publicationStatus && isset($validatedKey['publication_status'])) {
                $hasGsiKey = true;
            } elseif ($userId && isset($validatedKey['user_id'])) {
                $hasGsiKey = true;
            } elseif (isset($validatedKey['product_id'])) {
                $hasGsiKey = true; // product_id is always required for default case
            }

            if ($hasGsiKey && isset($validatedKey['review_id'])) {
                $query->afterKey($validatedKey);
                Log::debug('[Controller] Using pagination key', ['validated_key' => $validatedKey]);
            } else {
                Log::warning('[Controller] Invalid pagination key - missing required keys', [
                    'validated_key' => $validatedKey,
                    'has_gsi_key' => $hasGsiKey,
                    'has_review_id' => isset($validatedKey['review_id'])
                ]);
            }
        }

        // Execute query and capture LastEvaluatedKey from DynamoDB response
        $results = $query->get();

        // For reliable pagination, we need to use the raw DynamoDB client to get LastEvaluatedKey
        // The baopham/dynamodb package doesn't reliably expose LastEvaluatedKey
        $lastEvaluatedKeyFromDb = null;

        // Build the raw DynamoDB query parameters to get the actual LastEvaluatedKey
        $client = app(\BaoPham\DynamoDb\DynamoDbClientService::class)->getClient();
        $tableName = (new RatingAndReview())->getTable();

        $queryParams = [
            'TableName' => $tableName,
            'Limit' => $perPage,
        ];

        // Set up the index and key condition based on filters
        if ($publicationStatus) {
            $queryParams['IndexName'] = 'publication_status-index';
            $queryParams['KeyConditionExpression'] = 'publication_status = :publication_status';
            $queryParams['ExpressionAttributeValues'] = [':publication_status' => ['S' => $publicationStatus]];
        } elseif ($userId) {
            $queryParams['IndexName'] = 'user_id-index';
            $queryParams['KeyConditionExpression'] = 'user_id = :user_id';
            $queryParams['ExpressionAttributeValues'] = [':user_id' => ['S' => $userId]];
        } elseif ($productId) {
            $queryParams['IndexName'] = 'product_id-index';
            $queryParams['KeyConditionExpression'] = 'product_id = :product_id';
            $queryParams['ExpressionAttributeValues'] = [':product_id' => ['S' => $productId]];
        } else {
            // Default to product_id-index scan
            $queryParams['IndexName'] = 'product_id-index';
            // For scan operation without specific key condition
            unset($queryParams['KeyConditionExpression']);
            $queryParams = array_merge($queryParams, ['Select' => 'ALL_ATTRIBUTES']);
        }

        // Add pagination if cursor exists
        if ($lastEvaluatedKey) {
            $exclusiveStartKey = [];
            foreach ($lastEvaluatedKey as $key => $value) {
                $exclusiveStartKey[$key] = ['S' => $value];
            }
            $queryParams['ExclusiveStartKey'] = $exclusiveStartKey;
        }

        // Execute raw query to get LastEvaluatedKey
        try {
            if (isset($queryParams['KeyConditionExpression'])) {
                $rawResponse = $client->query($queryParams);
            } else {
                // Use scan for queries without key condition
                unset($queryParams['KeyConditionExpression']);
                $rawResponse = $client->scan($queryParams);
            }

            if (isset($rawResponse['LastEvaluatedKey'])) {
                $lastEvaluatedKeyFromDb = [];
                foreach ($rawResponse['LastEvaluatedKey'] as $key => $value) {
                    $lastEvaluatedKeyFromDb[$key] = reset($value); // Get the actual value from DynamoDB format
                }
            }

            Log::debug('[Controller] Raw DynamoDB response', [
                'last_evaluated_key' => $lastEvaluatedKeyFromDb,
                'count' => $rawResponse['Count'] ?? 0,
                'scanned_count' => $rawResponse['ScannedCount'] ?? 0
            ]);
        } catch (\Exception $e) {
            Log::error('[Controller] Failed to execute raw DynamoDB query', [
                'error' => $e->getMessage(),
                'query_params' => $queryParams
            ]);
        }

        // Use the actual LastEvaluatedKey from DynamoDB, not the last item
        $nextPageTokenData = $lastEvaluatedKeyFromDb;

        // Generate new cursor
        $cursor = $nextPageTokenData ? $this->cursorPaginationService->encodeCursor($nextPageTokenData) : null;

        // Log cursor generation for debugging
        Log::debug('[Controller] Generated cursor', [
            'next_page_token_data' => $nextPageTokenData,
            'cursor' => $cursor,
            'results_count' => $results->count(),
            'per_page' => $perPage
        ]);

        // Sort by creation date (newest first)
        $sortedResults = $results->sortByDesc('created_at');

        // Handle cache invalidation if requested
        if ($request->boolean('invalidate_cache')) {
            $this->cloudFrontService->invalidatePaths(['/api/reviews']);
        }

        // Create resource collection with pagination metadata
        $collection = RatingAndReviewResource::collection($sortedResults->values());
        $currentPage = $request->input('page', 1);
        $path = url('/api/reviews');
        $queryParams = $request->except(['page']);

        // Build response metadata and links
        $additionalData = [];
        $links = ['first' => $path . '?' . http_build_query(array_merge($queryParams, ['page' => 1]))];
        // New cursor-based pagination only
        $additionalData['pagination'] = [
            'has_more' => $cursor !== null,
            'cursor' => $cursor
        ];

        $additionalData['links'] = $links;
        $additionalData['meta'] = [
            'current_page' => $currentPage,
            'per_page' => $perPage,
            'path' => $path,
            'from' => (($currentPage - 1) * $perPage) + 1,
            'to' => (($currentPage - 1) * $perPage) + $results->count(),
        ];

        // Add debugging info in development environments
        // if (app()->environment('local', 'development')) {
        //     $additionalData['debug'] = [
        //         'total_results_on_page' => $results->count(),
        //         'query_details' => [
        //             'primary_filter_status' => $publicationStatus,
        //             'primary_filter_user' => $userId,
        //             'primary_filter_product' => $productId,
        //         ],
        //         'dynamodb_last_key_for_next_token' => $nextPageTokenData
        //     ];
        // }

        $collection->additional($additionalData);
        return $collection;
    }

    /**
     * Check if there are any reviews with a 'pending' publication status.
     *
     * Uses a direct DynamoDB query for efficiency.
     *
     * @return JsonResponse Containing a boolean 'has_pending_reviews'.
     */
    public function hasPendingReviews(): JsonResponse
    {
        $client = app(\BaoPham\DynamoDb\DynamoDbClientService::class)->getClient();
        $tableName = (new RatingAndReview())->getTable();
        $result = $client->query([
            'TableName' => $tableName,
            'IndexName' => 'publication_status-index',
            'KeyConditionExpression' => 'publication_status = :status',
            'ExpressionAttributeValues' => [
                ':status' => ['S' => 'pending']
            ],
            'Select' => 'COUNT',
            'Limit' => 1
        ]);
        $hasPending = $result['Count'] > 0;
        return response()->json([
            'data' => ['has_pending_reviews' => $hasPending],
            'meta' => ['timestamp' => now()->toIso8601String()]
        ]);
    }

    /**
     * Validate reviewId parameter format.
     *
     * @param string $reviewId The review ID to validate.
     * @throws \Illuminate\Validation\ValidationException If validation fails.
     */
    private function validateReviewId(string $reviewId): void
    {
        if (empty($reviewId)) {
            throw new \Illuminate\Validation\ValidationException(
                validator([], []),
                response()->json(['message' => 'Review ID cannot be empty'], 422)
            );
        }

        if (strlen($reviewId) > 255) {
            throw new \Illuminate\Validation\ValidationException(
                validator([], []),
                response()->json(['message' => 'Review ID is too long (maximum 255 characters)'], 422)
            );
        }

        if (!preg_match('/^[a-zA-Z0-9\-_]+$/', $reviewId)) {
            throw new \Illuminate\Validation\ValidationException(
                validator([], []),
                response()->json(['message' => 'Review ID contains invalid characters. Only alphanumeric characters, hyphens, and underscores are allowed'], 422)
            );
        }
    }

    /**
     * Legacy function used by admin panel - DO NOT MODIFY
     *
     * "BK" refers to backup implementation required by existing admin panel.
     * Returns counts for each review status plus total.
     *
     * @return JsonResponse
     */
    public function getReviewCountsByStatusBK(): JsonResponse
    {
        $statuses = ['pending', 'published', 'rejected'];
        $counts = [];
        foreach ($statuses as $status) {
            $reviewCollection = RatingAndReview::query()
                ->where('publication_status', $status)
                ->usingIndex('publication_status-index')
                ->get();
            $counts[$status] = $reviewCollection->count();
        }
        $counts['total'] = array_sum($counts);
        return response()->json([
            'data' => $counts,
            'meta' => ['timestamp' => now()->toIso8601String()]
        ]);
    }
}
