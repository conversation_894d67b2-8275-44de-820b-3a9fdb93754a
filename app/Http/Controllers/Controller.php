<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;

/**
 * @OA\Info(
 *     title="Mumzworld Ratings and Reviews API",
 *     version="1.0.0",
 *     description="API for managing product ratings and reviews, including support for media uploads and translations.",
 *     @OA\Contact(
 *         name="Mumzworld",
 *         email="<EMAIL>"
 *     )
 * )
 * @OA\Server(
 *     url="/api",
 *     description="API base URL"
 * )
 */
class Controller extends BaseController
{
    use AuthorizesRequests, ValidatesRequests;
}