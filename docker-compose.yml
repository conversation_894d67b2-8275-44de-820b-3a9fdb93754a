services:
  app:
    env_file: .env
    build:
      context: .
      dockerfile: ./docker/application/${DOCKER_APP_DOCKERFILE:-Dockerfile.prod}
      args:
        APP_ENV: ${APP_ENV:-production}
    ports:
      - "${DOCKER_APP_HTTP_PORT:-80}:80"
      - "${DOCKER_APP_HTTPS_PORT:-443}:443"
    volumes:
      - ./:/var/www/html
    environment:
      FRANKENPHP_CONFIG: "/var/www/html/docker/application/frankenphp.ini"
      # <PERSON><PERSON> will read these from the .env file loaded into the container
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION}
      # DYNAMODB_ENDPOINT: ${DYNAMODB_LOCAL_ENDPOINT:-http://dynamodb}:${DOCKER_DYNAMODB_HOST_PORT:-8000}
      # DYNAMODB_ENDPOINT: http://dynamodb:8000
      # Other app-specific vars from .env are also available due to `env_file: .env`
    networks:
      - app-network

  mysql:
    env_file: .env
    build:
      context: ./docker/mysql
      dockerfile: Dockerfile
    ports:
      - "${DOCKER_MYSQL_HOST_PORT:-3306}:3306"
    environment:
      MYSQL_ROOT_PASSWORD: ${DOCKER_MYSQL_ROOT_PASSWORD:-rootpassword}
      MYSQL_DATABASE: ${DB_DATABASE:-app_db}
      MYSQL_USER: ${DB_USERNAME:-app_user}
      MYSQL_PASSWORD: ${DB_PASSWORD:-userpassword}
    volumes:
      - ./docker/data/mysql:/var/lib/mysql
    networks:
      - app-network

  redis:
    env_file: .env
    build:
      context: ./docker/redis
      dockerfile: Dockerfile
    ports:
      - "${DOCKER_REDIS_HOST_PORT:-6379}:6379"
    volumes:
      - ./docker/data/redis:/data
    networks:
      - app-network

  dynamodb:
    env_file: .env
    build:
      context: ./docker/dynamodb
      dockerfile: Dockerfile
    ports:
      - "${DOCKER_DYNAMODB_HOST_PORT:-8000}:8000"
    volumes:
      - ./docker/data/dynamodb:/data/dynamodb
    command: "-jar DynamoDBLocal.jar -sharedDb -dbPath /data/dynamodb"
    networks:
      - app-network

  horizon:
    env_file: .env
    build:
      context: .
      dockerfile: ./docker/horizon/${DOCKER_HORIZON_DOCKERFILE:-Dockerfile.prod}
      args:
        APP_ENV: ${APP_ENV:-production}
    volumes:
      - ./:/var/www/html
    environment:
      FRANKENPHP_CONFIG: "/var/www/html/docker/horizon/frankenphp.ini"
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION}
      # DYNAMODB_ENDPOINT: ${DYNAMODB_LOCAL_ENDPOINT:-http://dynamodb}:${DOCKER_DYNAMODB_HOST_PORT:-8000}
      # Other vars from .env are also available
    depends_on:
      - app
      - redis
      - mysql
    networks:
      - app-network

  phpmyadmin:
    env_file: .env
    build:
      context: ./docker/phpmyadmin
      dockerfile: Dockerfile
    ports:
      - "${DOCKER_PHPMYADMIN_HOST_PORT:-8080}:80"
    environment:
      PMA_HOST: mysql # This correctly refers to the mysql service name
      PMA_PORT: 3306  # Internal port of the mysql service
      MYSQL_ROOT_PASSWORD: ${DOCKER_MYSQL_ROOT_PASSWORD:-rootpassword} # Must match mysql service
    depends_on:
      - mysql
    networks:
      - app-network

  redis-commander:
    env_file: .env
    build:
      context: ./docker/redis-commander
      dockerfile: Dockerfile
    ports:
      - "${DOCKER_REDIS_COMMANDER_HOST_PORT:-8081}:8081"
    environment:
      REDIS_HOSTS: "local:redis:6379" # 'redis' is the service name, 6379 is its internal port
    depends_on:
      - redis
    networks:
      - app-network

  dynamodb-admin:
    env_file: .env
    build:
      context: ./docker/dynamodb-admin
      dockerfile: Dockerfile
    ports:
      - "${DOCKER_DYNAMODB_ADMIN_HOST_PORT:-8001}:8001"
    environment:
      DYNAMO_ENDPOINT: ${DYNAMODB_LOCAL_ENDPOINT:-http://dynamodb}:${DOCKER_DYNAMODB_HOST_PORT:-8000} # Use env var with fallback
      AWS_REGION: ${AWS_DEFAULT_REGION:-us-local-1} # Use region from .env
      AWS_ACCESS_KEY_ID: dynamodblocal # For DynamoDB Local
      AWS_SECRET_ACCESS_KEY: secret # For DynamoDB Local
    depends_on:
      - dynamodb
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

# Service definitions continue above, network definition is complete.
# Any further global configurations like secrets or configs would go here if needed. 