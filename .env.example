APP_NAME="Mumzworld Laravel Service Starter Kit"
APP_ENV=local
APP_KEY=base64:qaTsGB0As7B7Uwh/vR5qk6XS1YU1zarGkLnCKV3iVHw=
APP_DEBUG=true
APP_URL=http://localhost
APP_PORT=7000

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
# LOG_STACK=stdout
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=dynamo
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=database_name
DB_USERNAME=database_user
DB_PASSWORD=database_password

SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis

CACHE_STORE=redis
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# AWS General Configuration
# These are standard AWS SDK environment variables.
# For the 'aws' DynamoDB connection profile, these (or an IAM role) will be used.
# For the 'local' DynamoDB connection profile, AWS_DEFAULT_REGION and DYNAMODB_LOCAL_ENDPOINT are used,
# but key/secret are hardcoded in config/dynamodb.php as 'dynamodblocal'/'secret'.
AWS_ACCESS_KEY_ID="AWS_ACCESS_KEY_ID" # For actual AWS, your access key. For local, can leave blank if using hardcoded local creds, or put dummy.
AWS_SECRET_ACCESS_KEY="AWS_SECRET_ACCESS_KEY" # For actual AWS, your secret key. For local, can leave blank or put dummy.
AWS_DEFAULT_REGION=us-east-1 # Your target AWS region, or a local one like us-local-1 / us-east-1 for DynamoDB Local.
AWS_SESSION_TOKEN="AWS_SESSION_TOKEN" # Optional: if using temporary credentials

AWS_BUCKET= # Your S3 bucket if using S3
AWS_USE_PATH_STYLE_ENDPOINT=false # Set to true for MinIO or other S3 compatibles if needed

# DynamoDB Specific Configuration (used by baopham/laravel-dynamodb package)
# Selects the connection profile from config/dynamodb.php ('local' or 'aws')
DYNAMODB_CONNECTION=local
# DynamoDB Service
DOCKER_DYNAMODB_HOST_PORT=8000
# DynamoDB Admin Service
DOCKER_DYNAMODB_ADMIN_HOST_PORT=8001

# Endpoint for DynamoDB Local. For actual AWS, this should NOT be set (SDK uses regional endpoint).
DYNAMODB_LOCAL_ENDPOINT=http://dynamodb
# DYNAMODB_ENDPOINT=http://dynamodb:8000 # Kept for broader compatibility if some code still uses it, but DYNAMODB_LOCAL_ENDPOINT is preferred for the local connection.
# Enable debug logging for the DynamoDB client (true or false)
DYNAMODB_DEBUG=true

VITE_APP_NAME="${APP_NAME}"

# Docker Environment Variables
# These are used by docker-compose.yml to configure the services.
# You should create a .env file by copying this example.env and fill these out.
# Application Service (FrankenPHP)
DOCKER_APP_DOCKERFILE=Dockerfile.dev # Options: Dockerfile.dev, Dockerfile.prod
DOCKER_APP_HTTP_PORT=7001
DOCKER_APP_HTTPS_PORT=443


# phpMyAdmin Service
DOCKER_PHPMYADMIN_HOST_PORT=8080

# Redis Commander Service
DOCKER_REDIS_COMMANDER_HOST_PORT=8081

# Horizon Service
DOCKER_HORIZON_DOCKERFILE=Dockerfile.dev # Options: Dockerfile.dev, Dockerfile.prod

# translate 
GOOGLE_TRANSLATE_API_KEY=
GOOGLE_TRANSLATE_ENDPOINT=https://translation.googleapis.com/language/translate/v2

# CloudFront CDN
CLOUDFRONT_DISTRIBUTION_ID=
CLOUDFRONT_KEY=
CLOUDFRONT_SECRET=
CLOUDFRONT_REGION=us-east-1

#
DB_CONNECTION=dynamodb
DYNAMODB_TABLE=ratings_and_reviews