; FrankenPHP configuration
; See https://frankenphp.dev/docs/config/

; Enable Laravel Octane support
frankenphp.octane.enabled = on

; Error handling configuration
log_errors = On
error_log = /dev/stderr
display_errors = Off
display_startup_errors = Off
html_errors = Off
error_reporting = E_ALL

; Set timezone to UTC (default for Laravel)
date.timezone = UTC

; Error reporting settings
error_reporting = E_ALL
display_errors = Off
display_startup_errors = Off
log_errors = On
error_log = /dev/stderr
report_memleaks = On
html_errors = Off

; Set error format to include full path
error_prepend_string = "PHP Error: "
error_append_string = ""

; Increase memory limits
memory_limit = 128M

; Ensure errors go to stderr not stdout
expose_php = Off 