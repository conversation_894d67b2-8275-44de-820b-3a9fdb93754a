# Dockerfile for Development (inherits from FrankenPHP base)
FROM dunglas/frankenphp:1-php8.3-bookworm AS frankenphp_base

# Install system dependencies for common PHP extensions
# hadolint ignore=DL3008
RUN apt-get update && apt-get install -y --no-install-recommends \
    libzip-dev \
    zip \
    unzip \
    libpng-dev \
    libjpeg-dev \
    libwebp-dev \
    libpq-dev \
    libfreetype6-dev \
    # For gd: libfreetype6-dev is often a dependency but might be covered by libpng/jpeg
    # For exif: no specific system libs typically needed beyond what PHP provides
    # For pcntl/sockets: usually built-in or requires no extra system libs
    # For intl: libicu-dev
    libicu-dev \
    && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
# hadolint ignore=DL3018
RUN docker-php-ext-configure gd --with-jpeg --with-webp --with-freetype \
    && docker-php-ext-install -j$(nproc) \
    pdo_mysql \
    bcmath \
    gd \
    exif \
    pcntl \
    sockets \
    intl \
    zip \
    opcache

# Install redis extension via PECL
RUN pecl install redis && docker-php-ext-enable redis

# Install Xdebug (for development)
RUN pecl install xdebug && docker-php-ext-enable xdebug

# Set working directory
WORKDIR /var/www/html

# Copy Xdebug configuration
COPY docker/application/xdebug.ini /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini

# Copy custom FrankenPHP configuration to enable opcache for dev
COPY docker/application/frankenphp.ini /usr/local/etc/php/conf.d/zz-frankenphp-custom.ini
COPY docker/application/opcache-dev.ini /usr/local/etc/php/conf.d/opcache-dev.ini

# Install Composer globally
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer
RUN chmod +x /usr/bin/composer

# Copy composer files
COPY composer.json composer.lock ./

# Install composer dependencies (including dev dependencies)
RUN composer install --prefer-dist --no-scripts --no-progress --no-autoloader

# Copy application code
COPY . .

# Optimize autoloader and run post-install scripts
RUN composer dump-autoload --optimize && \
    composer run-script post-autoload-dump --no-dev

# Set permissions
RUN chown -R www-data:www-data storage bootstrap/cache && \
    chmod -R 775 storage bootstrap/cache

# Copy app start script
COPY docker/application/start_app_dev.sh /usr/local/bin/start_app_dev.sh
RUN chmod +x /usr/local/bin/start_app_dev.sh

# Expose port 80 for the web server (FrankenPHP default)
# Port 443 is also exposed by the base image if Caddy is used with HTTPS
EXPOSE 80

# Set the command
CMD ["/usr/local/bin/start_app_dev.sh"]