; FrankenPHP configuration for Horizon workers
; This configuration applies to the PHP environment running Horizon jobs

; If you were to use FrankenPHP to serve a dashboard or API related to Horizon (less common for workers):
; frankenphp.Caddyfile = "/path/to/your/Caddyfile.horizon"
frankenphp.octane.enabled = on

; Error handling configuration - same as main application
log_errors = On
error_log = /dev/stderr
display_errors = Off
display_startup_errors = Off
html_errors = Off
error_reporting = E_ALL
report_memleaks = On

; Set timezone to match Laravel
date.timezone = ${TZ}

; Memory limits - Horizon may need more memory for processing jobs
memory_limit = 128M

; Ensure errors go to stderr not stdout
expose_php = Off

; Set error format to include full path
error_prepend_string = "PHP Error: "
error_append_string = "" 