# Dockerfile for Horizon Production (inherits from FrankenPHP base)
FROM dunglas/frankenphp

WORKDIR /var/www/html

# Install system dependencies (if any specific to Horizon, otherwise often shared with app)
# hadolint ignore=DL3008
RUN apt-get update && apt-get install -y --no-install-recommends \
    git \
    zip \
    unzip \
    libzip-dev \
    libicu-dev \
    # Add any other system dependencies Horizon might need
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install PHP extensions (pcntl is crucial for Horizon, add others as needed)
RUN install-php-extensions \
    pdo_mysql \
    bcmath \
    pcntl \
    sockets \
    intl \
    zip
    # Add any other PHP extensions Horizon needs

# Install redis extension via PECL
RUN pecl install redis && docker-php-ext-enable redis

# Install Composer globally
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer
RUN chmod +x /usr/bin/composer

# Copy Composer files from project root
COPY composer.json composer.lock ./

# Install Composer dependencies (production: --no-dev)
RUN composer install --no-interaction --no-plugins --no-scripts --prefer-dist --no-dev \
    && composer clear-cache

# Copy the rest of the application code (Horizon is part of this) from project root
COPY . .

# Set permissions (if Horizon needs to write to specific dirs beyond what Laravel app does)
# Usually, Horizon runs as the same user and leverages Laravel's storage.
RUN chown -R www-data:www-data storage bootstrap/cache && \
    chmod -R 775 storage bootstrap/cache

# Copy horizon start script
COPY docker/horizon/start_horizon_prod.sh /usr/local/bin/start_horizon_prod.sh
RUN chmod +x /usr/local/bin/start_horizon_prod.sh

# Set the command
CMD ["/usr/local/bin/start_horizon_prod.sh"]

# Application code (including Horizon) is typically mounted via volumes.
# If building a production image or specific assets, uncomment and adjust:
# COPY . /var/www/html

# Optional: Install PHP extensions (e.g., pcntl for Horizon)
# RUN install-php-extensions pcntl pdo_mysql zip ...

# Command to run Horizon is specified in docker-compose.yml

# The command to run Horizon is specified in docker-compose.yml
# (e.g., "php artisan horizon")
# If you were to run FrankenPHP as a server for Horizon (not typical for workers), you'd configure it here. 