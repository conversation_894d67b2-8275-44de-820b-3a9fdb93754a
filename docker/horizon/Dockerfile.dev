# Dockerfile for Horizon Development (inherits from FrankenPHP base)
FROM dunglas/frankenphp

WORKDIR /var/www/html

# Install system dependencies (if any specific to Horizon dev tools, otherwise often shared with app)
# hadolint ignore=DL3008
RUN apt-get update && apt-get install -y --no-install-recommends \
    git \
    zip \
    unzip \
    libzip-dev \
    libicu-dev \
    # Add any other system dependencies Horizon might need for dev
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install PHP extensions (pcntl is crucial for Horizon, add others as needed)
RUN install-php-extensions \
    pdo_mysql \
    bcmath \
    pcntl \
    sockets \
    intl \
    zip
    # Add any other PHP extensions Horizon needs

# Install redis extension via PECL
RUN pecl install redis && docker-php-ext-enable redis

# Install Composer globally
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer
RUN chmod +x /usr/bin/composer

# Copy Composer files from project root
COPY composer.json composer.lock ./

# Install Composer dependencies (including dev requirements for Horizon development/testing)
RUN composer install --no-interaction --no-plugins --no-scripts --prefer-dist \
    && composer clear-cache

# Copy the rest of the application code (Horizon is part of this) from project root
COPY . .

# Set permissions (if Horizon needs to write to specific dirs beyond what Laravel app does)
RUN chown -R www-data:www-data storage bootstrap/cache && \
    chmod -R 775 storage bootstrap/cache

# Copy horizon start script
COPY docker/horizon/start_horizon_dev.sh /usr/local/bin/start_horizon_dev.sh
RUN chmod +x /usr/local/bin/start_horizon_dev.sh

# Expose port for any health checks or metrics if needed (Horizon itself doesn't serve HTTP)

# Set the command
CMD ["/usr/local/bin/start_horizon_dev.sh"]

# The command to run Horizon ("php artisan horizon") is specified in docker-compose.yml. 