# Laravel specific
/.phpunit.cache
/node_modules
/public/build
/public/hot
/public/storage
/storage/*.key
/storage/pail
/vendor
.env
.env.backup
.env.production
.phpactor.json
.phpunit.result.cache
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
/auth.json

# IDE specific
/.fleet
/.idea
/.nova
/.vscode
/.zed

# FrankenPHP
**/caddy
frankenphp
frankenphp-worker.php

# macOS specific
.DS_Store
.AppleDouble
.LSOverride
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Composer
composer.phar

# <PERSON><PERSON> specific additional ignores
.env.*.local
_ide_helper.php
_ide_helper_models.php
.phpstorm.meta.php

# ignore the dynamodb database file but keep the directory structure
docker/dynamodb/data/*
!docker/dynamodb/data/.gitkeep

# Docker data volumes
docker/data/

# Ignore untracked files
/docs/ARCHITECTURE.md
/docs/MIGRATION.md
.kiro/*